<?php

namespace Database\Factories\Accounting;

use App\Models\Accounting\AccountSubtype;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<AccountSubtype>
 */
class AccountSubtypeFactory extends Factory
{
    protected $model = AccountSubtype::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            //
        ];
    }
}
