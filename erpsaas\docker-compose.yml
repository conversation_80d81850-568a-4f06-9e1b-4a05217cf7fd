version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: arena_doviz_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - arena_doviz
    depends_on:
      - mysql
      - redis

  nginx:
    image: nginx:alpine
    container_name: arena_doviz_nginx
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    networks:
      - arena_doviz
    depends_on:
      - app

  mysql:
    image: mysql:8.0
    container_name: arena_doviz_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: arena_doviz
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_USER: arena_user
      MYSQL_PASSWORD: arena_password
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - arena_doviz

  redis:
    image: redis:6.2-alpine
    container_name: arena_doviz_redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - arena_doviz

  mailhog:
    image: mailhog/mailhog
    container_name: arena_doviz_mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"
      - "8025:8025"
    networks:
      - arena_doviz

networks:
  arena_doviz:
    driver: bridge

volumes:
  mysql_data:
  redis_data:
