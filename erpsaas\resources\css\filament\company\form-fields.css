/* Filament Repeater Styles */
.fi-fo-repeater.uncontained .fi-fo-repeater-item {
    @apply divide-y-0 rounded-none bg-none shadow-none ring-0 ring-gray-950/5 dark:divide-white/10 dark:bg-white/5 dark:ring-white/10;

    .fi-fo-repeater-item-header {
        @apply px-0;

        > h4 {
            @apply text-base font-semibold leading-6 text-gray-950 dark:text-white;
        }
    }

    .fi-fo-repeater-item-content {
        @apply py-4 px-0;
    }
}

.fi-fo-repeater-item {
    @apply divide-y divide-gray-200 rounded-xl bg-white dark:bg-gray-900;
}

/* Report Field Styles */
.fi-fo-field-wrp.report-hidden-label > div.grid.gap-y-2 > div.flex.items-center {
    @apply hidden;
}

.fi-fo-field-wrp.report-hidden-label {
    @apply lg:mt-8;
}

/* Choices.js select field overrides */
.choices:focus-visible {
    outline: none;
}

.choices__group {
    @apply text-gray-900 dark:text-white font-semibold;
}

.choices:not(.is-disabled) .choices__item {
    cursor: pointer;
}

/* Table Repeater Styles */
.table-repeater-component:not(.is-spreadsheet) {
    .table-repeater-row {
        @apply hover:bg-gray-50 dark:hover:bg-white/5 transition-colors;
    }

    .table-repeater-column {
        @apply p-3;
    }

    .table-repeater-header {
        @apply bg-gray-50 dark:bg-white/5 border-b border-gray-200 dark:border-white/5;
    }

    .table-repeater-row {
        @apply divide-x-0 !important;
    }

    .table-repeater-header tr {
        @apply divide-x-0 text-sm;
    }

    .table-repeater-header-column {
        @apply p-3 text-sm font-semibold text-gray-950 dark:text-white bg-gray-50 dark:bg-white/5;
    }

    /* Chrome, Safari, Edge, Opera */
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }
}

/* Excel/Spreadsheet styling */
.table-repeater-component.is-spreadsheet {
    .table-repeater-container {
        overflow-x: auto;
        max-width: 100%;
        -webkit-overflow-scrolling: touch;
    }

    .table-repeater-container:has(.choices.is-open) {
        overflow: visible;
    }

    .table-repeater-container table {
        min-width: 100%;
        width: max-content;
    }

    .table-repeater-container {
        border: 1px solid #e5e7eb !important;
        border-radius: 0 !important;
        @apply ring-0 !important;
    }

    .table-repeater-header {
        background-color: #f8f9fa !important;
    }

    .table-repeater-header-column {
        border: 1px solid #e5e7eb !important;
        background-color: #f8f9fa !important;
        font-weight: 600 !important;
        padding: 8px 12px !important;
    }

    .table-repeater-column {
        border: 1px solid #e5e7eb !important;
        padding: 8px 12px !important;
    }

    .table-repeater-column input {
        text-align: right !important;
    }

    .fi-input-wrapper,
    .fi-input {
        padding: 0 !important;
    }

    .fi-input-wrp,
    .fi-fo-file-upload .filepond--root {
        @apply ring-0 bg-transparent shadow-none rounded-none !important;
    }

    .fi-input-wrp input {
        @apply bg-transparent !important;
    }

    .table-repeater-column:focus-within {
        outline: 2px solid #2563eb !important;
        outline-offset: -2px !important;
        z-index: 1 !important;
    }

    input:focus,
    select:focus,
    textarea:focus {
        @apply ring-0 shadow-none !important;
        outline: none !important;
    }

    .table-repeater-row:nth-child(even) {
        background-color: #f9fafb;
    }

    .fi-fo-field-wrp:has(.fi-fo-checkbox-list),
    .fi-fo-field-wrp:has(.fi-checkbox-input),
    .fi-fo-field-wrp:has(.fi-fo-radio) {
        @apply py-2 px-3 !important;
    }

    .fi-fo-field-wrp:has(.fi-fo-toggle) {
        @apply inline-block mt-1 !important;
    }
}

.is-spreadsheet .table-repeater-column .fi-input-wrp-suffix {
    padding-right: 0 !important;
}

