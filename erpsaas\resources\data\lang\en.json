{"General": "General", "Bank account": "Bank account", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Taxes & Discounts": "Taxes & Discounts", "Sales tax": "Sales tax", "Purchase tax": "Purchase tax", "Sales discount": "Sales discount", "Purchase discount": "Purchase Discount", "Appearance": "Appearance", "Default": "<PERSON><PERSON><PERSON>", "Company Profile": "Company Profile", "Invoice": "Invoice", "Localization": "Localization", "Discount": "Discount", "Tax": "Tax", "Accounting": "Accounting", "Banking": "Banking", "Account": "Account", "Department": "Department", "Services": "Services", "Connected Accounts": "Connected Accounts", "Live Currency": "Live Currency", "Primary color": "Primary color", "amber": "amber", "blue": "blue", "cyan": "cyan", "emerald": "emerald", "fuchsia": "fuchsia", "gray": "gray", "green": "green", "indigo": "indigo", "lime": "lime", "neutral": "neutral", "orange": "orange", "pink": "pink", "purple": "purple", "red": "red", "rose": "rose", "sky": "sky", "slate": "slate", "stone": "stone", "teal": "teal", "violet": "violet", "yellow": "yellow", "zinc": "zinc", "Font": "Font", "Enabled": "Enabled", "Disabled": "Disabled", "Identification": "Identification", "Email": "Email", "Phone number": "Phone number", "Logo": "Logo", "Location Details": "Location Details", "Country": "Country", "State / Province": "State / Province", "Street Address": "Street Address", "City / Town": "City / Town", "Zip / Postal Code": "Zip / Postal Code", "Legal & Compliance": "Legal & Compliance", "Entity type": "Entity type", "Tax ID": "Tax ID", "Sole Proprietorship": "Sole Proprietorship", "General Partnership": "General Partnership", "Limited Partnership (LP)": "Limited Partnership (LP)", "Limited Liability Partnership (LLP)": "Limited Liability Partnership (LLP)", "Limited Liability Company (LLC)": "Limited Liability Company (LLC)", "Corporation": "Corporation", "Nonprofit": "Nonprofit", "Number prefix": "Number prefix", "Number digits": "Number digits", "Number next": "Number next", "Payment terms": "Payment terms", "Content": "Content", "Header": "Header", "Subheader": "Subheader", "Footer / notes": "Footer / notes", "Template": "Template", "Show logo": "Show logo", "Accent color": "Accent color", "Item name": "Item name", "Items": "Items", "Products": "Products", "Other": "Other", "Unit name": "Unit name", "Quantity": "Quantity", "Hours": "Hours", "Price name": "Price name", "Price": "Price", "Rate": "Rate", "Amount name": "Amount name", "Amount": "Amount", "Total": "Total", "Due Upon Receipt": "Due Upon Receipt", "Net 7": "Net 7", "Net 10": "Net 10", "Net 15": "Net 15", "Net 30": "Net 30", "Net 60": "Net 60", "Net 90": "Net 90", "Modern": "Modern", "Classic": "Classic", "Language": "Language", "Timezone": "Timezone", "Date & Time": "Date & Time", "Date format": "Date format", "Time format": "Time format", "Week start": "Week start", "Before number": "Before number", "After number": "After number", "Select position": "Select position", "Financial & Fiscal": "Financial & Fiscal", "Number format": "Number format", "Percent position": "Percent position", "Name": "Name", "Code": "Code", "Symbol": "Symbol", "Default :record": "Default :record", "Type": "Type", "Start date": "Start date", "End date": "End date", "Sales": "Sales", "Default :type :record": "Default :type :record", "Purchase": "Purchase", "Precision": "Precision", "Symbol position": "Symbol position", "Before amount": "Before amount", "After amount": "After amount", "Select a symbol position": "Select a symbol position", "Decimal separator": "Decimal separator", "Thousands separator": "Thousands separator", "Yes": "Yes", "No": "No", "Description": "Description", "Computation": "Computation", "Scope": "<PERSON><PERSON>", "Percentage": "Percentage", "Fixed": "Fixed", "None": "None", "Product": "Product", "Service": "Service", "Compound": "Compound", "Current balance": "Current balance", "Account Information": "Account Information", "Subtype": "Subtype", "Account number": "Account number", "Investment": "Investment", "Credit": "Credit", "Depository": "Depository", "Loan": "Loan", "Manager": "Manager", "Children": "Children", "All": "All", "Main": "Main", "Parent department": "Parent department", "Currency List": "Currency List", "Company Currencies": "Company Currencies", "Entity": "Entity", "Available": "Available", "Live rate": "Live rate", "Edit": "Edit", "Notes": "Notes", "Terms": "Terms", "Ending balance": "Ending balance", "Default :type :category": "Default :type :category", "Category": "Category", "Configuration": "Configuration", "Dates": "Dates", "Adjustment Details": "Adjustment Details", "Adjustments": "Adjustments", "Sellable Configuration": "Sellable Configuration", "Purchasable Configuration": "Purchasable Configuration", "Sale Information": "Sale Information", "Purchase Information": "Purchase Information", "Billing": "Billing", "Shipping": "Shipping", "General Information": "General Information", "Primary Contact": "Primary Contact", "Billing Address": "Billing Address", "Shipping Address": "Shipping Address", "Secondary Contacts": "Secondary Contacts", "Address Information": "Address Information", "Invoice Header": "Invoice Header", "Invoice Details": "Invoice Details", "Footer": "Footer", "Invoice Footer": "Invoice Footer", "Bill Details": "<PERSON>", "Create": "Create", "Estimate Header": "Estimate Header", "Estimate Details": "Estimate Details", "Estimate Footer": "Estimate Footer", "Scheduling": "Scheduling", "Scheduling Form": "Scheduling Form", "Approve": "Approve", "Frequency": "Frequency", "Dates & Time": "Dates & Time", "account": "account", "currency": "currency", "Estimate": "Estimate", "Column Labels": "Column Labels", "Budget Details": "Budget Details", "Budget Items": "Budget Items", "Budget Allocations": "Budget Allocations", "Account Selection": "Account Selection", "Custom": "Custom"}