{"$schema": "https://getcomposer.org/schema.json", "name": "andrewdwallo/erpsaas", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-bcmath": "*", "ext-intl": "*", "akaunting/laravel-money": "^6.0.2", "andrewdwallo/filament-companies": "^4.0", "andrewdwallo/filament-selectify": "^2.0", "andrewdwallo/transmatic": "^1.1", "awcodes/filament-table-repeater": "^3.0", "barryvdh/laravel-snappy": "^1.0", "codewithdennis/filament-simple-alert": "^3.0", "fakerphp/faker": "^1.24", "filament/filament": "^3.2", "guava/filament-clusters": "^1.1", "jaocero/radio-deck": "^1.2", "laravel/framework": "^12.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.10.1", "squirephp/model": "^3.4", "squirephp/repository": "^3.4", "symfony/intl": "^6.3"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15", "laravel/pail": "^1.2.2", "laravel/pint": "^1.24", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "pestphp/pest": "^3.0", "pestphp/pest-plugin-livewire": "^3.0", "spatie/laravel-ignition": "^2.4", "spatie/laravel-ray": "^1.36"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite --kill-others"], "test": ["@php artisan config:clear --ansi", "@php artisan test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}