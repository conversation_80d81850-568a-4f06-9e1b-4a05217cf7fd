<x-filament-panels::page>
    <div class="flex flex-col gap-y-6">
        <x-filament::tabs>
            @foreach($this->accountCategories as $categoryValue => $accountSubtypes)
                <x-filament::tabs.item
                    wire:key="tab-item-{{ $categoryValue }}"
                    :active="$activeTab === $categoryValue"
                    wire:click="$set('activeTab', '{{ $categoryValue }}')"
                    :badge="$accountSubtypes->sum('accounts_count')"
                >
                    {{ $this->getCategoryLabel($categoryValue) }}
                </x-filament::tabs.item>
            @endforeach
        </x-filament::tabs>

        @foreach($this->accountCategories as $categoryValue => $accountSubtypes)
            @if($activeTab === $categoryValue)
                <div
                    class="es-table__container overflow-hidden rounded-xl bg-white shadow-sm ring-1 ring-gray-950/5 dark:divide-white/10 dark:bg-gray-900 dark:ring-white/10">
                    <div class="es-table__header-ctn"></div>
                    <div class="es-table__content overflow-x-auto">
                        <table
                            class="es-table w-full min-w-[70rem] divide-y divide-gray-200 text-start text-sm dark:divide-white/5">
                            <colgroup>
                                <col span="1" style="width: 12.5%;">
                                <col span="1" style="width: 20%;">
                                <col span="1" style="width: 35%;">
                                <col span="1" style="width: 15%;">
                                <col span="1" style="width: 10%;">
                                <col span="1" style="width: 7.5%;">
                            </colgroup>
                            @foreach($accountSubtypes as $accountSubtype)
                                <tbody
                                    class="es-table__rowgroup divide-y divide-gray-200 whitespace-nowrap dark:divide-white/5">
                                <!-- Subtype Name Header Row -->
                                <tr class="es-table__row--header bg-gray-50 dark:bg-white/5">
                                    <td colspan="6" class="es-table__cell px-4 py-4">
                                        <div class="es-table__row-content flex items-center space-x-2">
                                            <span
                                                class="es-table__row-title text-gray-800 dark:text-gray-200 font-semibold tracking-wider">
                                                {{ $accountSubtype->name }}
                                            </span>
                                            <x-tooltip
                                                text="{!! $accountSubtype->description !!}"
                                                icon="heroicon-o-question-mark-circle"
                                                placement="right"
                                                maxWidth="300"
                                            />
                                        </div>
                                    </td>
                                </tr>

                                <!-- Chart Rows -->
                                @forelse($accountSubtype->accounts as $account)
                                    <tr class="es-table__row">
                                        <td colspan="1" class="es-table__cell px-4 py-4">{{ $account->code }}</td>
                                        <td colspan="1" class="es-table__cell px-4 py-4">
                                            {{ $account->name }}
                                            <br>
                                            <small class="text-gray-500 dark:text-gray-400">
                                                @if($account->last_transaction_date)
                                                    Last transaction
                                                    on {{ \Illuminate\Support\Carbon::parse($account->last_transaction_date)->toDefaultDateFormat() }}
                                                @else
                                                    No transactions for this account
                                                @endif
                                            </small>
                                        </td>
                                        <td colspan="2"
                                            class="es-table__cell px-4 py-4">{{ $account->description }}</td>
                                        <td colspan="1" class="es-table__cell px-4 py-4">
                                            @if($account->archived)
                                                <x-filament::badge color="gray" size="sm">
                                                    Archived
                                                </x-filament::badge>
                                            @endif
                                        </td>
                                        <td colspan="1" class="es-table__cell px-4 py-4">
                                            <div>
                                                @if($account->default === false && !$account->adjustment)
                                                    {{ ($this->editAccountAction)(['account' => $account->id]) }}
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <!-- No Accounts Available Row -->
                                    <tr class="es-table__row">
                                        <td colspan="5"
                                            class="es-table__cell px-4 py-4 italic text-xs text-gray-500 dark:text-gray-400">
                                            {{ __("You haven't added any {$accountSubtype->name} accounts yet.") }}
                                        </td>
                                    </tr>
                                @endforelse

                                <!-- Add New Account Row -->
                                <tr class="es-table__row">
                                    <td colspan="5" class="es-table__cell px-4 py-4">
                                        {{ ($this->createAccountAction)(['accountSubtype' => $accountSubtype->id]) }}
                                    </td>
                                </tr>
                                </tbody>
                            @endforeach
                        </table>
                    </div>
                    <div class="es-table__footer-ctn border-t border-gray-200"></div>
                </div>
            @endif
        @endforeach
    </div>
</x-filament-panels::page>
