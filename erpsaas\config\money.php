<?php

return [

    'defaults' => [

        'currency' => env('MONEY_DEFAULTS_CURRENCY', 'USD'),

        'convert' => env('MONEY_DEFAULTS_CONVERT', false),

    ],

    'currencies' => [

        'AFN' => [
            'name' => 'Afghani',
            'code' => '971',
            'entity' => 'Afghanistan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '؋',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'EUR' => [
            'name' => 'Euro',
            'code' => '978',
            'entity' => 'Åland Islands',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '€',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'ALL' => [
            'name' => 'Lek',
            'code' => '008',
            'entity' => 'Albania',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'L',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'DZD' => [
            'name' => 'Algerian Dinar',
            'code' => '012',
            'entity' => 'Algeria',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'د.ج',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'USD' => [
            'name' => 'US Dollar',
            'code' => '840',
            'entity' => 'American Samoa',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'AOA' => [
            'name' => 'Kwanza',
            'code' => '973',
            'entity' => 'Angola',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Kz',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'XCD' => [
            'name' => 'East Caribbean Dollar',
            'code' => '951',
            'entity' => 'Anguilla',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'ARS' => [
            'name' => 'Argentine Peso',
            'code' => '032',
            'entity' => 'Argentina',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'AMD' => [
            'name' => 'Armenian Dram',
            'code' => '051',
            'entity' => 'Armenia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '֏',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'AWG' => [
            'name' => 'Aruban Florin',
            'code' => '533',
            'entity' => 'Aruba',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'ƒ',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'AUD' => [
            'name' => 'Australian Dollar',
            'code' => '036',
            'entity' => 'Australia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'AZN' => [
            'name' => 'Azerbaijan Manat',
            'code' => '944',
            'entity' => 'Azerbaijan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₼',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BSD' => [
            'name' => 'Bahamian Dollar',
            'code' => '044',
            'entity' => 'Bahamas',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BHD' => [
            'name' => 'Bahraini Dinar',
            'code' => '048',
            'entity' => 'Bahrain',
            'precision' => 3,
            'subunit' => 1000,
            'symbol' => '.د.ب',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BDT' => [
            'name' => 'Taka',
            'code' => '050',
            'entity' => 'Bangladesh',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '৳',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BBD' => [
            'name' => 'Barbados Dollar',
            'code' => '052',
            'entity' => 'Barbados',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BYN' => [
            'name' => 'Belarusian Ruble',
            'code' => '933',
            'entity' => 'Belarus',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Br',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'BZD' => [
            'name' => 'Belize Dollar',
            'code' => '084',
            'entity' => 'Belize',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'BZ$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'XOF' => [
            'name' => 'CFA Franc BCEAO',
            'code' => '952',
            'entity' => 'Benin',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'CFA',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BMD' => [
            'name' => 'Bermudian Dollar',
            'code' => '060',
            'entity' => 'Bermuda',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'INR' => [
            'name' => 'Indian Rupee',
            'code' => '356',
            'entity' => 'India',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₹',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BTN' => [
            'name' => 'Ngultrum',
            'code' => '064',
            'entity' => 'Bhutan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Nu.',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BOB' => [
            'name' => 'Boliviano',
            'code' => '068',
            'entity' => 'Bolivia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$b',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BOV' => [
            'name' => 'Mvdol',
            'code' => '984',
            'entity' => 'Bolivia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'BOV',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BAM' => [
            'name' => 'Convertible Mark',
            'code' => '977',
            'entity' => 'Bosnia And Herzegovina',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'KM',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'BWP' => [
            'name' => 'Pula',
            'code' => '072',
            'entity' => 'Botswana',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'P',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'NOK' => [
            'name' => 'Norwegian Krone',
            'code' => '578',
            'entity' => 'Norway',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'kr',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'BRL' => [
            'name' => 'Brazilian Real',
            'code' => '986',
            'entity' => 'Brazil',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'R$',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'BND' => [
            'name' => 'Brunei Dollar',
            'code' => '096',
            'entity' => 'Brunei Darussalam',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'BGN' => [
            'name' => 'Bulgarian Lev',
            'code' => '975',
            'entity' => 'Bulgaria',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'лв',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'BIF' => [
            'name' => 'Burundi Franc',
            'code' => '108',
            'entity' => 'Burundi',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'FBu',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'CVE' => [
            'name' => 'Cabo Verde Escudo',
            'code' => '132',
            'entity' => 'Cabo Verde',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'KHR' => [
            'name' => 'Riel',
            'code' => '116',
            'entity' => 'Cambodia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '៛',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'XAF' => [
            'name' => 'CFA Franc BEAC',
            'code' => '950',
            'entity' => 'Cameroon',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'FCFA',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'CAD' => [
            'name' => 'Canadian Dollar',
            'code' => '124',
            'entity' => 'Canada',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'KYD' => [
            'name' => 'Cayman Islands Dollar',
            'code' => '136',
            'entity' => 'Cayman Islands',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'CLP' => [
            'name' => 'Chilean Peso',
            'code' => '152',
            'entity' => 'Chile',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => '.',
        ],

        'CNY' => [
            'name' => 'Yuan Renminbi',
            'code' => '156',
            'entity' => 'China',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '¥',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'COP' => [
            'name' => 'Colombian Peso',
            'code' => '170',
            'entity' => 'Colombia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'KMF' => [
            'name' => 'Comorian Franc ',
            'code' => '174',
            'entity' => 'Comoros',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'CF',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'CDF' => [
            'name' => 'Congolese Franc',
            'code' => '976',
            'entity' => 'Congo',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'FC',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'NZD' => [
            'name' => 'New Zealand Dollar',
            'code' => '554',
            'entity' => 'New Zealand',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'CRC' => [
            'name' => 'Costa Rican Colon',
            'code' => '188',
            'entity' => 'Costa Rica',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₡',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'CUP' => [
            'name' => 'Cuban Peso',
            'code' => '192',
            'entity' => 'Cuba',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₱',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'CUC' => [
            'name' => 'Peso Convertible',
            'code' => '931',
            'entity' => 'Cuba',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'ANG' => [
            'name' => 'Netherlands Antillean Guilder',
            'code' => '532',
            'entity' => 'Curaçao',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'ƒ',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'CZK' => [
            'name' => 'Czech Koruna',
            'code' => '203',
            'entity' => 'Czech Republic',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Kč',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'DKK' => [
            'name' => 'Danish Krone',
            'code' => '208',
            'entity' => 'Denmark',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'kr',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'DJF' => [
            'name' => 'Djibouti Franc',
            'code' => '262',
            'entity' => 'Djibouti',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'Fdj',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'DOP' => [
            'name' => 'Dominican Peso',
            'code' => '214',
            'entity' => 'Dominican Republic',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'RD$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'EGP' => [
            'name' => 'Egyptian Pound',
            'code' => '818',
            'entity' => 'Egypt',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '£',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SVC' => [
            'name' => 'El Salvador Colon',
            'code' => '222',
            'entity' => 'El Salvador',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'ERN' => [
            'name' => 'Nakfa',
            'code' => '232',
            'entity' => 'Eritrea',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Nfk',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SZL' => [
            'name' => 'Lilangeni',
            'code' => '748',
            'entity' => 'Eswatini',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'E',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'ETB' => [
            'name' => 'Ethiopian Birr',
            'code' => '230',
            'entity' => 'Ethiopia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Br',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'FKP' => [
            'name' => 'Falkland Islands Pound',
            'code' => '238',
            'entity' => 'Falkland Islands (Malvinas)',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '£',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'FJD' => [
            'name' => 'Fiji Dollar',
            'code' => '242',
            'entity' => 'Fiji',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'XPF' => [
            'name' => 'CFP Franc',
            'code' => '953',
            'entity' => 'New Caledonia',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => '₣',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'GMD' => [
            'name' => 'Dalasi',
            'code' => '270',
            'entity' => 'Gambia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'D',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'GEL' => [
            'name' => 'Lari',
            'code' => '981',
            'entity' => 'Georgia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₾',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'GHS' => [
            'name' => 'Ghana Cedi',
            'code' => '936',
            'entity' => 'Ghana',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'GH₵',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'GIP' => [
            'name' => 'Gibraltar Pound',
            'code' => '292',
            'entity' => 'Gibraltar',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '£',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'GTQ' => [
            'name' => 'Quetzal',
            'code' => '320',
            'entity' => 'Guatemala',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Q',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'GBP' => [
            'name' => 'Pound Sterling',
            'code' => '826',
            'entity' => 'United Kingdom',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '£',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'GNF' => [
            'name' => 'Guinean Franc',
            'code' => '324',
            'entity' => 'Guinea',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'FG',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'GYD' => [
            'name' => 'Guyana Dollar',
            'code' => '328',
            'entity' => 'Guyana',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'HTG' => [
            'name' => 'Gourde',
            'code' => '332',
            'entity' => 'Haiti',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'G',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'HNL' => [
            'name' => 'Lempira',
            'code' => '340',
            'entity' => 'Honduras',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'L',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'HKD' => [
            'name' => 'Hong Kong Dollar',
            'code' => '344',
            'entity' => 'Hong Kong',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'HUF' => [
            'name' => 'Forint',
            'code' => '348',
            'entity' => 'Hungary',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Ft',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'ISK' => [
            'name' => 'Iceland Krona',
            'code' => '352',
            'entity' => 'Iceland',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'kr',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'IDR' => [
            'name' => 'Rupiah',
            'code' => '360',
            'entity' => 'Indonesia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Rp',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'IRR' => [
            'name' => 'Iranian Rial',
            'code' => '364',
            'entity' => 'Iran',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '﷼',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'IQD' => [
            'name' => 'Iraqi Dinar',
            'code' => '368',
            'entity' => 'Iraq',
            'precision' => 3,
            'subunit' => 1000,
            'symbol' => 'ع.د',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'ILS' => [
            'name' => 'New Israeli Sheqel',
            'code' => '376',
            'entity' => 'Israel',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₪',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'JMD' => [
            'name' => 'Jamaican Dollar',
            'code' => '388',
            'entity' => 'Jamaica',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'J$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'JPY' => [
            'name' => 'Yen',
            'code' => '392',
            'entity' => 'Japan',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => '¥',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'JOD' => [
            'name' => 'Jordanian Dinar',
            'code' => '400',
            'entity' => 'Jordan',
            'precision' => 3,
            'subunit' => 100,
            'symbol' => 'JD',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'KZT' => [
            'name' => 'Tenge',
            'code' => '398',
            'entity' => 'Kazakhstan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₸',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'KES' => [
            'name' => 'Kenyan Shilling',
            'code' => '404',
            'entity' => 'Kenya',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'KSh',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'KPW' => [
            'name' => 'North Korean Won',
            'code' => '408',
            'entity' => 'North Korea',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₩',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'KRW' => [
            'name' => 'Won',
            'code' => '410',
            'entity' => 'South Korea',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => '₩',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'KWD' => [
            'name' => 'Kuwaiti Dinar',
            'code' => '414',
            'entity' => 'Kuwait',
            'precision' => 3,
            'subunit' => 1000,
            'symbol' => 'KD',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'KGS' => [
            'name' => 'Som',
            'code' => '417',
            'entity' => 'Kyrgyzstan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'лв',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'LAK' => [
            'name' => 'Lao Kip',
            'code' => '418',
            'entity' => 'Laos',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₭',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'LBP' => [
            'name' => 'Lebanese Pound',
            'code' => '422',
            'entity' => 'Lebanon',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '£',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'LSL' => [
            'name' => 'Loti',
            'code' => '426',
            'entity' => 'Lesotho',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'M',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'ZAR' => [
            'name' => 'Rand',
            'code' => '710',
            'entity' => 'South Africa',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'R',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'LRD' => [
            'name' => 'Liberian Dollar',
            'code' => '430',
            'entity' => 'Liberia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'LYD' => [
            'name' => 'Libyan Dinar',
            'code' => '434',
            'entity' => 'Libya',
            'precision' => 3,
            'subunit' => 1000,
            'symbol' => 'LD',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'CHF' => [
            'name' => 'Swiss Franc',
            'code' => '756',
            'entity' => 'Switzerland',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Fr.',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => "'",
        ],

        'MOP' => [
            'name' => 'Pataca',
            'code' => '446',
            'entity' => 'Macao',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'MOP$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MKD' => [
            'name' => 'Denar',
            'code' => '807',
            'entity' => 'North Macedonia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'ден',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'MGA' => [
            'name' => 'Malagasy Ariary',
            'code' => '969',
            'entity' => 'Madagascar',
            'precision' => 2,
            'subunit' => 5,
            'symbol' => 'Ar',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MWK' => [
            'name' => 'Malawi Kwacha',
            'code' => '454',
            'entity' => 'Malawi',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'MK',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MYR' => [
            'name' => 'Malaysian Ringgit',
            'code' => '458',
            'entity' => 'Malaysia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'RM',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MVR' => [
            'name' => 'Rufiyaa',
            'code' => '462',
            'entity' => 'Maldives',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Rf',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MRU' => [
            'name' => 'Ouguiya',
            'code' => '929',
            'entity' => 'Mauritania',
            'precision' => 2,
            'subunit' => 5,
            'symbol' => 'UM',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'MUR' => [
            'name' => 'Mauritius Rupee',
            'code' => '480',
            'entity' => 'Mauritius',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₨',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MXN' => [
            'name' => 'Mexican Peso',
            'code' => '484',
            'entity' => 'Mexico',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MDL' => [
            'name' => 'Moldovan Leu',
            'code' => '498',
            'entity' => 'Moldova',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'lei',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MNT' => [
            'name' => 'Tugrik',
            'code' => '496',
            'entity' => 'Mongolia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₮',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MAD' => [
            'name' => 'Moroccan Dirham',
            'code' => '504',
            'entity' => 'Morocco',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'د.م.',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MZN' => [
            'name' => 'Mozambique Metical',
            'code' => '943',
            'entity' => 'Mozambique',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'MT',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'MMK' => [
            'name' => 'Kyat',
            'code' => '104',
            'entity' => 'Myanmar',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'K',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'NAD' => [
            'name' => 'Namibia Dollar',
            'code' => '516',
            'entity' => 'Namibia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'NPR' => [
            'name' => 'Nepalese Rupee',
            'code' => '524',
            'entity' => 'Nepal',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₨',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'NIO' => [
            'name' => 'Cordoba Oro',
            'code' => '558',
            'entity' => 'Nicaragua',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'C$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'NGN' => [
            'name' => 'Naira',
            'code' => '566',
            'entity' => 'Nigeria',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₦',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'OMR' => [
            'name' => 'Rial Omani',
            'code' => '512',
            'entity' => 'Oman',
            'precision' => 3,
            'subunit' => 1000,
            'symbol' => '﷼',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'PKR' => [
            'name' => 'Pakistan Rupee',
            'code' => '586',
            'entity' => 'Pakistan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₨',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'PAB' => [
            'name' => 'Balboa',
            'code' => '590',
            'entity' => 'Panama',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'B/.',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'PGK' => [
            'name' => 'Kina',
            'code' => '598',
            'entity' => 'Papua New Guinea',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'K',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'PYG' => [
            'name' => 'Guarani',
            'code' => '600',
            'entity' => 'Paraguay',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'Gs',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'PEN' => [
            'name' => 'Sol',
            'code' => '604',
            'entity' => 'Peru',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'S/.',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'PHP' => [
            'name' => 'Philippine Peso',
            'code' => '608',
            'entity' => 'Philippines',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₱',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'PLN' => [
            'name' => 'Zloty',
            'code' => '985',
            'entity' => 'Poland',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'zł',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => ' ',
        ],

        'QAR' => [
            'name' => 'Qatari Rial',
            'code' => '634',
            'entity' => 'Qatar',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '﷼',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'RON' => [
            'name' => 'Romanian Leu',
            'code' => '946',
            'entity' => 'Romania',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'lei',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'RUB' => [
            'name' => 'Russian Ruble',
            'code' => '643',
            'entity' => 'Russia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₽',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'RWF' => [
            'name' => 'Rwanda Franc',
            'code' => '646',
            'entity' => 'Rwanda',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'R₣',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SHP' => [
            'name' => 'Saint Helena Pound',
            'code' => '654',
            'entity' => 'Saint Helena',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '£',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'WST' => [
            'name' => 'Tala',
            'code' => '882',
            'entity' => 'Samoa',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'WS$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'STN' => [
            'name' => 'Dobra',
            'code' => '930',
            'entity' => 'São Tomé and Príncipe',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Db',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'SAR' => [
            'name' => 'Saudi Riyal',
            'code' => '682',
            'entity' => 'Saudi Arabia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '﷼',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'RSD' => [
            'name' => 'Serbian Dinar',
            'code' => '941',
            'entity' => 'Serbia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'дин.',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'SCR' => [
            'name' => 'Seychelles Rupee',
            'code' => '690',
            'entity' => 'Seychelles',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₨',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SLL' => [
            'name' => 'Leone',
            'code' => '694',
            'entity' => 'Sierra Leone',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Le',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SLE' => [
            'name' => 'Leone',
            'code' => '925',
            'entity' => 'Sierra Leone',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Le',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SGD' => [
            'name' => 'Singapore Dollar',
            'code' => '702',
            'entity' => 'Singapore',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'S$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SBD' => [
            'name' => 'Solomon Islands Dollar',
            'code' => '090',
            'entity' => 'Solomon Islands',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SOS' => [
            'name' => 'Somali Shilling',
            'code' => '706',
            'entity' => 'Somalia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'S',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SSP' => [
            'name' => 'South Sudanese Pound',
            'code' => '728',
            'entity' => 'South Sudan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '£',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'LKR' => [
            'name' => 'Sri Lanka Rupee',
            'code' => '144',
            'entity' => 'Sri Lanka',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₨',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SDG' => [
            'name' => 'Sudanese Pound',
            'code' => '938',
            'entity' => 'Sudan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'ج.س.',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SRD' => [
            'name' => 'Surinam Dollar',
            'code' => '968',
            'entity' => 'Suriname',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'SEK' => [
            'name' => 'Swedish Krona',
            'code' => '752',
            'entity' => 'Sweden',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'kr',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'SYP' => [
            'name' => 'Syrian Pound',
            'code' => '760',
            'entity' => 'Syria',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '£S',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'TWD' => [
            'name' => 'New Taiwan Dollar',
            'code' => '901',
            'entity' => 'Taiwan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'NT$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'TJS' => [
            'name' => 'Somoni',
            'code' => '972',
            'entity' => 'Tajikistan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'SM',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'TZS' => [
            'name' => 'Tanzanian Shilling',
            'code' => '834',
            'entity' => 'Tanzania',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'TSh',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'THB' => [
            'name' => 'Baht',
            'code' => '764',
            'entity' => 'Thailand',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '฿',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'TOP' => [
            'name' => 'Pa’anga',
            'code' => '776',
            'entity' => 'Tonga',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'T$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'TTD' => [
            'name' => 'Trinidad and Tobago Dollar',
            'code' => '780',
            'entity' => 'Trinidad and Tobago',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'TT$',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'TND' => [
            'name' => 'Tunisian Dinar',
            'code' => '788',
            'entity' => 'Tunisia',
            'precision' => 3,
            'subunit' => 1000,
            'symbol' => 'د.ت',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'TRY' => [
            'name' => 'Turkish Lira',
            'code' => '949',
            'entity' => 'Turkey',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₺',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'TMT' => [
            'name' => 'Turkmenistan New Manat',
            'code' => '934',
            'entity' => 'Turkmenistan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'T',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'UGX' => [
            'name' => 'Uganda Shilling',
            'code' => '800',
            'entity' => 'Uganda',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'USh',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'UAH' => [
            'name' => 'Hryvnia',
            'code' => '980',
            'entity' => 'Ukraine',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '₴',
            'symbol_first' => false,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'AED' => [
            'name' => 'UAE Dirham',
            'code' => '784',
            'entity' => 'United Arab Emirates',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'د.إ',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'UYU' => [
            'name' => 'Peso Uruguayo',
            'code' => '858',
            'entity' => 'Uruguay',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$U',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '.',
        ],

        'UZS' => [
            'name' => 'Uzbekistan Sum',
            'code' => '860',
            'entity' => 'Uzbekistan',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'лв',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'VUV' => [
            'name' => 'Vatu',
            'code' => '548',
            'entity' => 'Vanuatu',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => 'VT',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'VES' => [
            'name' => 'Bolívar Soberano',
            'code' => '928',
            'entity' => 'Venezuela',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Bs.S',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'VED' => [
            'name' => 'Bolívar Soberano',
            'code' => '926',
            'entity' => 'Venezuela',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'Bs.',
            'symbol_first' => true,
            'decimal_mark' => ',',
            'thousands_separator' => '',
        ],

        'VND' => [
            'name' => 'Dong',
            'code' => '704',
            'entity' => 'Vietnam',
            'precision' => 0,
            'subunit' => 1,
            'symbol' => '₫',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'YER' => [
            'name' => 'Yemeni Rial',
            'code' => '886',
            'entity' => 'Yemen',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '﷼',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'ZMW' => [
            'name' => 'Zambian Kwacha',
            'code' => '967',
            'entity' => 'Zambia',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => 'ZK',
            'symbol_first' => false,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],

        'ZWL' => [
            'name' => 'Zimbabwe Dollar',
            'code' => '932',
            'entity' => 'Zimbabwe',
            'precision' => 2,
            'subunit' => 100,
            'symbol' => '$',
            'symbol_first' => true,
            'decimal_mark' => '.',
            'thousands_separator' => ',',
        ],
    ],

];
