# Arena Doviz - Technical Specifications

## System Requirements

### Server Requirements
- **Operating System**: Windows Server 2019/2022, Windows 10/11, or Linux (Ubuntu 20.04+)
- **Web Server**: Nginx 1.18+ or Apache 2.4+
- **PHP**: Version 8.2 or higher with extensions:
  - <PERSON><PERSON>ath, Ctype, Fileinfo, JSON, Mbstring, OpenSSL, PDO, Tokenizer, XML, GD, Imagick
- **Database**: MySQL 8.0+ or PostgreSQL 13+
- **Memory**: Minimum 4GB RAM, Recommended 8GB+
- **Storage**: Minimum 20GB, Recommended 100GB+ for documents and backups
- **Redis**: Version 6.0+ for caching and session management

### Client Requirements
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **JavaScript**: Enabled
- **Screen Resolution**: Minimum 1024x768, Optimized for 1920x1080
- **Internet Connection**: Broadband connection recommended

## Database Design

### Core Tables Structure

#### Users Table
```sql
CREATE TABLE users (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    surname VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    company_id BIGINT UNSIGNED,
    role ENUM('admin', 'accountant', 'viewer', 'courier'),
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

#### Customers Table
```sql
CREATE TABLE customers (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    surname VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    company_name VARCHAR(255),
    notes TEXT,
    whatsapp_group_id VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_by BIGINT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

#### Currencies Table
```sql
CREATE TABLE currencies (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(10) NOT NULL UNIQUE, -- Extended to support location codes like USD_IST, USD_TBZ
    base_code VARCHAR(3) NOT NULL, -- Base currency code (USD, EUR, etc.)
    location_id BIGINT UNSIGNED, -- Associated location for location-specific currencies
    name VARCHAR(100) NOT NULL,
    symbol VARCHAR(10),
    decimal_places TINYINT DEFAULT 2,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (location_id) REFERENCES locations(id)
);
```

#### Locations Table
```sql
CREATE TABLE locations (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(10) NOT NULL UNIQUE,
    address TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### Exchange Rates Table
```sql
CREATE TABLE exchange_rates (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    currency_id BIGINT UNSIGNED NOT NULL,
    location_id BIGINT UNSIGNED NOT NULL,
    buy_rate DECIMAL(15,6) NOT NULL,
    sell_rate DECIMAL(15,6) NOT NULL,
    effective_date DATE NOT NULL,
    created_by BIGINT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_rate (currency_id, location_id, effective_date)
);
```

#### Transactions Table
```sql
CREATE TABLE transactions (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    transaction_number VARCHAR(50) NOT NULL UNIQUE,
    type ENUM('buy', 'sell', 'transfer', 'swift_in', 'swift_out', 'cash_deposit', 'cash_withdrawal', 'buy_sell_combined'),
    customer_from_id BIGINT UNSIGNED, -- For buy transactions or transfers (source)
    customer_to_id BIGINT UNSIGNED, -- For sell transactions or transfers (destination)
    currency_from_id BIGINT UNSIGNED NOT NULL, -- Source currency
    currency_to_id BIGINT UNSIGNED, -- Destination currency (for exchanges)
    location_id BIGINT UNSIGNED NOT NULL,
    amount_from DECIMAL(15,6) NOT NULL, -- Amount in source currency
    amount_to DECIMAL(15,6), -- Amount in destination currency
    buy_rate DECIMAL(15,6), -- Rate for buying
    sell_rate DECIMAL(15,6), -- Rate for selling
    commission_type ENUM('percentage', 'fixed'),
    commission_value DECIMAL(10,6),
    commission_currency_id BIGINT UNSIGNED, -- Currency for commission deduction
    commission_from_source BOOLEAN DEFAULT TRUE, -- Whether commission is from source or destination currency
    total_amount DECIMAL(15,6),
    counterparty VARCHAR(255),
    bank_name VARCHAR(255), -- Iranian bank name
    bank_transaction_number VARCHAR(100),
    internal_transaction_number VARCHAR(100),
    iranian_bank_id BIGINT UNSIGNED, -- Reference to Iranian banks table
    batch_id VARCHAR(50), -- For batch processing of IRR transactions
    notes TEXT,
    status ENUM('pending', 'approved', 'completed', 'cancelled') DEFAULT 'pending',
    approved_by BIGINT UNSIGNED,
    approved_at TIMESTAMP NULL,
    created_by BIGINT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL -- Soft delete - never permanently delete
);
```

#### Iranian Banks Table
```sql
CREATE TABLE iranian_banks (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(10),
    swift_code VARCHAR(11),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### Debit Notes Table (for delivery management)
```sql
CREATE TABLE debit_notes (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    note_number VARCHAR(50) NOT NULL UNIQUE,
    customer_from_id BIGINT UNSIGNED NOT NULL,
    customer_to_id BIGINT UNSIGNED, -- Can be customer or courier
    courier_id BIGINT UNSIGNED,
    currency_id BIGINT UNSIGNED NOT NULL,
    amount DECIMAL(15,6) NOT NULL,
    purpose VARCHAR(255),
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
    receipt_signed BOOLEAN DEFAULT FALSE,
    receipt_photo_path VARCHAR(500),
    created_by BIGINT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
);
```

### Indexing Strategy
```sql
-- Performance indexes
CREATE INDEX idx_transactions_customer_from_date ON transactions(customer_from_id, created_at);
CREATE INDEX idx_transactions_customer_to_date ON transactions(customer_to_id, created_at);
CREATE INDEX idx_transactions_currency_location ON transactions(currency_from_id, location_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_batch ON transactions(batch_id);
CREATE INDEX idx_exchange_rates_lookup ON exchange_rates(currency_id, location_id, effective_date);
CREATE INDEX idx_customers_active ON customers(is_active, created_at);
CREATE INDEX idx_currencies_location ON currencies(location_id, base_code);
```

## API Specifications

### Authentication
- **Method**: Laravel Sanctum token-based authentication
- **Token Expiration**: 24 hours (configurable)
- **Rate Limiting**: 60 requests per minute per user

### Core API Endpoints

#### Authentication Endpoints
```
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
GET  /api/auth/user
```

#### Customer Management
```
GET    /api/customers              # List customers with pagination
POST   /api/customers              # Create new customer
GET    /api/customers/{id}         # Get customer details
PUT    /api/customers/{id}         # Update customer
DELETE /api/customers/{id}         # Soft delete customer
GET    /api/customers/{id}/balance # Get customer balance
```

#### Transaction Management
```
GET    /api/transactions           # List transactions with filters
POST   /api/transactions           # Create new transaction
GET    /api/transactions/{id}      # Get transaction details
PUT    /api/transactions/{id}      # Update transaction
POST   /api/transactions/{id}/approve # Approve transaction
POST   /api/transactions/{id}/cancel  # Cancel transaction
```

#### Exchange Rates
```
GET    /api/exchange-rates         # Get current rates
POST   /api/exchange-rates         # Update rates
GET    /api/exchange-rates/history # Get rate history
```

### Response Format
```json
{
    "success": true,
    "data": {
        // Response data
    },
    "message": "Operation completed successfully",
    "meta": {
        "pagination": {
            "current_page": 1,
            "total_pages": 10,
            "per_page": 20,
            "total": 200
        }
    }
}
```

## Security Specifications

### Authentication & Authorization
- **Password Policy**: Minimum 8 characters, mixed case, numbers, special characters
- **Session Management**: Redis-based sessions with 2-hour timeout
- **Role-Based Access Control**: Spatie Permission package implementation
- **API Security**: Rate limiting, CORS configuration, CSRF protection

### Data Protection
- **Encryption**: AES-256 encryption for sensitive data
- **Database**: Encrypted connections (SSL/TLS)
- **File Storage**: Encrypted file storage with access controls
- **Audit Logging**: All CRUD operations logged with user attribution

### Security Headers
```
Content-Security-Policy: default-src 'self'
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
Permissions-Policy: geolocation=(), microphone=(), camera=()
```

## Performance Specifications

### Response Time Requirements
- **Page Load**: < 2 seconds for standard pages
- **Transaction Processing**: < 1 second for simple transactions
- **Report Generation**: < 5 seconds for standard reports
- **API Responses**: < 500ms for simple queries

### Caching Strategy
- **Application Cache**: Redis with 1-hour TTL for frequently accessed data
- **Query Cache**: Database query result caching
- **Session Cache**: Redis-based session storage
- **Static Assets**: Browser caching with 1-year expiration

### Database Performance
- **Connection Pooling**: Maximum 20 concurrent connections
- **Query Optimization**: All queries under 100ms execution time
- **Indexing**: Comprehensive indexing strategy for all lookup tables
- **Partitioning**: Transaction table partitioning by date for large datasets

## Integration Specifications

### WhatsApp Integration
- **API**: WhatsApp Business API or third-party service
- **Message Templates**: Pre-defined templates for transaction notifications
- **Group Management**: Automated group creation and member management
- **Rate Limiting**: Respect WhatsApp API rate limits

### Currency Exchange API
- **Primary Provider**: ExchangeRate-API
- **Fallback Providers**: Fixer.io, CurrencyLayer
- **Update Frequency**: Every 15 minutes during business hours
- **Error Handling**: Graceful fallback to cached rates

### File Storage
- **Local Storage**: For development and small deployments
- **Cloud Storage**: AWS S3 or compatible for production
- **File Types**: PDF, JPG, PNG, DOC, DOCX (max 10MB per file)
- **Security**: Signed URLs for secure file access

## Deployment Specifications

### Production Environment
```yaml
# Docker Compose Configuration
version: '3.8'
services:
  app:
    image: arena-doviz:latest
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    volumes:
      - ./storage:/var/www/storage
      - ./public/uploads:/var/www/public/uploads
  
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=secure_password
      - MYSQL_DATABASE=arena_doviz
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:6.2-alpine
    volumes:
      - redis_data:/data
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

### Monitoring & Logging
- **Application Monitoring**: Laravel Telescope for development, Sentry for production
- **Server Monitoring**: System resource monitoring with alerts
- **Log Management**: Centralized logging with log rotation
- **Backup Monitoring**: Automated backup verification and alerts

### Backup Strategy
- **Database Backups**: Daily full backups, hourly incremental
- **File Backups**: Daily synchronization to cloud storage
- **Retention Policy**: 30 days local, 1 year cloud storage
- **Recovery Testing**: Monthly backup restoration tests

## Quality Assurance

### Testing Strategy
- **Unit Tests**: 90%+ code coverage for business logic
- **Feature Tests**: Complete workflow testing
- **Integration Tests**: External API and service testing
- **Performance Tests**: Load testing for 100+ concurrent users

### Code Quality
- **Standards**: PSR-12 coding standards
- **Static Analysis**: PHPStan level 8
- **Code Review**: Required for all changes
- **Documentation**: Comprehensive inline documentation

### Deployment Process
- **Staging Environment**: Mirror of production for testing
- **CI/CD Pipeline**: Automated testing and deployment
- **Blue-Green Deployment**: Zero-downtime deployments
- **Rollback Procedures**: Automated rollback on deployment failure
